<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل الرسوم البيانية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; border-left: 4px solid #f44336; }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        button { background: #2196f3; color: white; border: none; padding: 10px 15px; margin: 5px; border-radius: 3px; cursor: pointer; }
        pre { background: white; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 تشخيص مشاكل الرسوم البيانية</h1>
    
    <div class="debug-section">
        <h2>1. فحص الاتصال بـ API</h2>
        <button onclick="testConnection()">اختبار الاتصال</button>
        <div id="connectionResult"></div>
    </div>

    <div class="debug-section">
        <h2>2. فحص endpoints الرسوم البيانية</h2>
        <button onclick="testAllEndpoints()">اختبار جميع endpoints</button>
        <div id="endpointsResult"></div>
    </div>

    <div class="debug-section">
        <h2>3. فحص قاعدة البيانات</h2>
        <button onclick="testDatabase()">اختبار قاعدة البيانات</button>
        <div id="databaseResult"></div>
    </div>

    <div class="debug-section">
        <h2>4. فحص المستخدم الحالي</h2>
        <button onclick="testCurrentUser()">فحص المستخدم</button>
        <div id="userResult"></div>
    </div>

    <div class="debug-section">
        <h2>5. سجل الأخطاء</h2>
        <button onclick="clearLog()">مسح السجل</button>
        <div id="errorLog"></div>
    </div>

    <script>
        let errorLog = [];

        // تسجيل جميع الأخطاء
        window.addEventListener('error', function(e) {
            logError('JavaScript Error', e.message + ' at ' + e.filename + ':' + e.lineno);
        });

        function logError(type, message) {
            const timestamp = new Date().toLocaleString('ar-SA');
            errorLog.push({ type, message, timestamp });
            updateErrorLog();
        }

        function updateErrorLog() {
            const logDiv = document.getElementById('errorLog');
            if (errorLog.length === 0) {
                logDiv.innerHTML = '<p>لا توجد أخطاء مسجلة</p>';
                return;
            }
            
            let html = '<h3>الأخطاء المسجلة:</h3>';
            errorLog.forEach(error => {
                html += `<div class="error"><strong>${error.type}</strong> (${error.timestamp})<br>${error.message}</div>`;
            });
            logDiv.innerHTML = html;
        }

        function clearLog() {
            errorLog = [];
            updateErrorLog();
        }

        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<p>جاري الاختبار...</p>';

            try {
                const response = await fetch('api/index.php/stats/general', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_id: 1 })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="success">✅ الاتصال بـ API يعمل بشكل صحيح</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ خطأ في الاتصال: ${response.status} ${response.statusText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ فشل الاتصال: ${error.message}</div>`;
                logError('Connection Test', error.message);
            }
        }

        async function testAllEndpoints() {
            const resultDiv = document.getElementById('endpointsResult');
            resultDiv.innerHTML = '<p>جاري اختبار endpoints...</p>';

            const endpoints = [
                'charts/age-groups',
                'charts/stock-levels', 
                'charts/monthly-trends',
                'charts/active-tasks'
            ];

            const testData = { user_id: 1, center_id: 1 };
            let results = [];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`api/index.php/${endpoint}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(endpoint.includes('monthly-trends') ? {...testData, months: 6} : testData)
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        results.push(`✅ ${endpoint}: يعمل بشكل صحيح`);
                    } else {
                        results.push(`❌ ${endpoint}: ${data.message || 'خطأ غير معروف'}`);
                        logError('Endpoint Test', `${endpoint}: ${data.message || response.statusText}`);
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint}: ${error.message}`);
                    logError('Endpoint Test', `${endpoint}: ${error.message}`);
                }
            }

            let html = '<h3>نتائج اختبار endpoints:</h3>';
            results.forEach(result => {
                const className = result.startsWith('✅') ? 'success' : 'error';
                html += `<div class="${className}">${result}</div>`;
            });
            resultDiv.innerHTML = html;
        }

        async function testDatabase() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.innerHTML = '<p>جاري فحص قاعدة البيانات...</p>';

            try {
                // اختبار الجداول الأساسية
                const tables = ['children', 'medicines', 'contraceptives', 'tasks'];
                let results = [];

                for (const table of tables) {
                    try {
                        const response = await fetch('api/index.php/stats/general', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ user_id: 1, table_check: table })
                        });

                        if (response.ok) {
                            results.push(`✅ جدول ${table}: متاح`);
                        } else {
                            results.push(`❌ جدول ${table}: غير متاح`);
                        }
                    } catch (error) {
                        results.push(`❌ جدول ${table}: خطأ في الاتصال`);
                    }
                }

                let html = '<h3>حالة الجداول:</h3>';
                results.forEach(result => {
                    const className = result.startsWith('✅') ? 'success' : 'error';
                    html += `<div class="${className}">${result}</div>`;
                });
                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ خطأ في فحص قاعدة البيانات: ${error.message}</div>`;
                logError('Database Test', error.message);
            }
        }

        function testCurrentUser() {
            const resultDiv = document.getElementById('userResult');
            
            // فحص localStorage
            const savedUser = localStorage.getItem('currentNurseUser');
            
            if (savedUser) {
                try {
                    const user = JSON.parse(savedUser);
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ المستخدم موجود في localStorage:</h3>
                            <pre>${JSON.stringify(user, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ خطأ في تحليل بيانات المستخدم: ${error.message}</div>`;
                    logError('User Test', 'Invalid user data in localStorage');
                }
            } else {
                resultDiv.innerHTML = '<div class="warning">⚠️ لا يوجد مستخدم مسجل في localStorage</div>';
                logError('User Test', 'No user found in localStorage');
            }

            // فحص المتغير العام
            if (typeof currentUser !== 'undefined' && currentUser) {
                resultDiv.innerHTML += `
                    <div class="success">
                        <h3>✅ المتغير العام currentUser:</h3>
                        <pre>${JSON.stringify(currentUser, null, 2)}</pre>
                    </div>
                `;
            } else {
                resultDiv.innerHTML += '<div class="warning">⚠️ المتغير العام currentUser غير محدد</div>';
                logError('User Test', 'Global currentUser variable is undefined');
            }
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateErrorLog();
            console.log('🔍 صفحة تشخيص الرسوم البيانية جاهزة');
            
            // فحص تلقائي للمستخدم
            setTimeout(testCurrentUser, 1000);
        });
    </script>
</body>
</html>
