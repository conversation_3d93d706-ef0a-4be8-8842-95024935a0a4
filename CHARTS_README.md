# قسم تحليل البيانات - نظام إدارة المراكز الصحية

## نظرة عامة

تم إضافة قسم تحليل البيانات المتقدم إلى لوحة التحكم الرئيسية، والذي يحتوي على رسوم بيانية تفاعلية تعرض معلومات حقيقية من قاعدة البيانات.

## الرسوم البيانية المتاحة

### 1. 📊 توزيع الأطفال حسب الفئات العمرية
- **النوع**: رسم بياني دائري (Pie Chart)
- **البيانات**: عدد الأطفال المسجلين مقسمين حسب الفئات العمرية
- **الفئات**: 
  - 0-6 أشهر
  - 6-12 شهر  
  - 1-2 سنة
  - 2-5 سنوات
  - 5+ سنوات

### 2. 📦 مستويات المخزون
- **النوع**: رسم بياني عمودي (Bar Chart)
- **البيانات**: 
  - مخزون الأدوية (أعلى 10 أنواع)
  - مخزون وسائل منع الحمل (أعلى 10 أنواع)
- **المعلومات**: الكمية المتوفرة لكل نوع

### 3. 📈 الاتجاهات الشهرية
- **النوع**: رسم بياني خطي (Line Chart)
- **البيانات**:
  - عدد الأطفال المسجلين شهرياً
  - عدد التلقيحات المكتملة شهرياً
- **الفترة**: آخر 6 أو 12 شهر (قابل للتخصيص)

### 4. ✅ حالة المهام
- **النوع**: رسم بياني دائري مجوف (Doughnut Chart)
- **البيانات**: توزيع المهام حسب الحالة
- **الحالات**:
  - مكتملة
  - قيد التنفيذ
  - معلقة
  - ملغية

## الميزات التقنية

### 🎨 التصميم
- تصميم متجاوب يتكيف مع جميع أحجام الشاشات
- دعم الوضع المظلم والفاتح
- ألوان متناسقة مع تصميم النظام الحالي
- تأثيرات بصرية متقدمة وانتقالات سلسة

### ⚡ الأداء
- تحميل تدريجي للرسوم البيانية
- تحسين استهلاك الذاكرة
- معالجة الأخطاء المتقدمة
- تحديث ذكي للبيانات

### 🔄 التفاعل
- أزرار تحديث فردية لكل رسم بياني
- تحديث شامل لجميع الرسوم البيانية
- تحميل الرسوم البيانية كصور PNG
- اختيار فترة زمنية للاتجاهات الشهرية

## API Endpoints الجديدة

### 1. بيانات الفئات العمرية
```
POST /api/index.php/charts/age-groups
Body: {
  "user_id": 123,
  "center_id": 456
}
```

### 2. بيانات المخزون
```
POST /api/index.php/charts/stock-levels
Body: {
  "user_id": 123,
  "center_id": 456
}
```

### 3. بيانات الاتجاهات الشهرية
```
POST /api/index.php/charts/monthly-trends
Body: {
  "user_id": 123,
  "center_id": 456,
  "months": 6
}
```

### 4. بيانات المهام النشطة
```
POST /api/index.php/charts/active-tasks
Body: {
  "user_id": 123,
  "center_id": 456
}
```

## دوال JavaScript الجديدة

### الدوال الرئيسية
- `createAgeGroupsChart()` - إنشاء رسم الفئات العمرية
- `createStockLevelsChart()` - إنشاء رسم المخزون
- `createMonthlyTrendsChart()` - إنشاء رسم الاتجاهات
- `createTasksStatusChart()` - إنشاء رسم المهام

### دوال التحديث
- `refreshAllCharts()` - تحديث جميع الرسوم البيانية
- `refreshAgeGroupsChart()` - تحديث رسم الفئات العمرية
- `refreshStockChart()` - تحديث رسم المخزون
- `refreshTrendsChart()` - تحديث رسم الاتجاهات
- `refreshTasksChart()` - تحديث رسم المهام

### دوال المساعدة
- `downloadChart(chartId)` - تحميل رسم بياني كصورة
- `initializeCharts()` - تهيئة جميع الرسوم البيانية
- `updateChartTheme()` - تحديث ألوان الرسوم البيانية

## أدوات التطوير والاختبار

### اختصارات لوحة المفاتيح
- `Ctrl + Shift + T` - اختبار بالبيانات التجريبية
- `Ctrl + Shift + R` - تحديث الرسوم البيانية
- `Ctrl + Shift + M` - قياس أداء التحميل

### أدوات وحدة التحكم
```javascript
// اختبار بالبيانات التجريبية
window.chartsDebug.test();

// قياس الأداء
window.chartsDebug.measure();

// تحديث الرسوم البيانية
window.chartsDebug.refresh();

// إعادة تهيئة
window.chartsDebug.init();

// الحصول على الألوان الحالية
window.chartsDebug.colors();

// إعادة تعيين جميع الرسوم البيانية
window.chartsDebug.reset();
```

## التحسينات المضافة

### 1. معالجة الأخطاء
- عرض رسائل خطأ واضحة
- إمكانية إعادة المحاولة
- تسجيل مفصل للأخطاء في وحدة التحكم

### 2. حالات التحميل
- مؤشرات تحميل أثناء جلب البيانات
- رسائل "لا توجد بيانات" عند عدم وجود محتوى
- تأثيرات بصرية أثناء التحديث

### 3. الاستجابة
- تخطيط متكيف للشاشات الصغيرة
- إخفاء عناصر غير ضرورية في الطباعة
- تحسين العرض على الأجهزة المحمولة

## المكتبات المستخدمة

- **Chart.js v4.4.0** - مكتبة الرسوم البيانية التفاعلية
- **Font Awesome** - الأيقونات
- **Cairo Font** - الخط العربي

## ملاحظات مهمة

1. **الأمان**: جميع API endpoints تتطلب مصادقة المستخدم
2. **الأداء**: الرسوم البيانية تُحدث تلقائياً عند تغيير المستخدم
3. **التوافق**: يعمل مع جميع المتصفحات الحديثة
4. **البيانات**: جميع البيانات مستمدة من قاعدة البيانات الحقيقية

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **الرسوم البيانية لا تظهر**
   - تأكد من تسجيل دخول المستخدم
   - تحقق من وجود بيانات في قاعدة البيانات
   - افتح وحدة التحكم للتحقق من الأخطاء

2. **بطء في التحميل**
   - تحقق من اتصال الإنترنت
   - راجع أداء الخادم
   - استخدم `window.chartsDebug.measure()` لقياس الأداء

3. **ألوان غير صحيحة**
   - تأكد من تطبيق الوضع الصحيح (فاتح/مظلم)
   - استخدم `window.chartsDebug.colors()` للتحقق من الألوان

## التطوير المستقبلي

### ميزات مقترحة
- إضافة المزيد من أنواع الرسوم البيانية
- تصدير البيانات إلى Excel/PDF
- مرشحات زمنية متقدمة
- مقارنات بين المراكز المختلفة
- تنبيهات ذكية عند انخفاض المخزون

---

تم تطوير هذا النظام بعناية فائقة لضمان الأداء الأمثل وسهولة الاستخدام. 🚀
