<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار APIs الرسوم البيانية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار APIs الرسوم البيانية</h1>
    
    <div class="test-container">
        <h2>📊 اختبار API الفئات العمرية</h2>
        <button class="test-button" onclick="testAgeGroups()">اختبار بيانات الفئات العمرية</button>
        <div id="ageGroupsResult" class="result"></div>
    </div>

    <div class="test-container">
        <h2>📦 اختبار API المخزون</h2>
        <button class="test-button" onclick="testStockLevels()">اختبار بيانات المخزون</button>
        <div id="stockResult" class="result"></div>
    </div>

    <div class="test-container">
        <h2>📈 اختبار API الاتجاهات الشهرية</h2>
        <button class="test-button" onclick="testMonthlyTrends()">اختبار الاتجاهات الشهرية</button>
        <div id="trendsResult" class="result"></div>
    </div>

    <div class="test-container">
        <h2>✅ اختبار API المهام</h2>
        <button class="test-button" onclick="testActiveTasks()">اختبار بيانات المهام</button>
        <div id="tasksResult" class="result"></div>
    </div>

    <div class="test-container">
        <h2>🔄 اختبار شامل</h2>
        <button class="test-button" onclick="testAll()">اختبار جميع APIs</button>
        <div id="allResult" class="result"></div>
    </div>

    <script>
        // بيانات اختبار
        const testData = {
            user_id: 1,
            center_id: 1
        };

        async function makeRequest(endpoint, data = testData) {
            try {
                const response = await fetch(`api/index.php/charts/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                return { success: true, data: result };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result, testName) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleString('ar-SA');
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = `✅ ${testName} - نجح الاختبار (${timestamp})\n\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ ${testName} - فشل الاختبار (${timestamp})\n\nخطأ: ${result.error}`;
            }
        }

        async function testAgeGroups() {
            const result = await makeRequest('age-groups');
            displayResult('ageGroupsResult', result, 'اختبار الفئات العمرية');
        }

        async function testStockLevels() {
            const result = await makeRequest('stock-levels');
            displayResult('stockResult', result, 'اختبار المخزون');
        }

        async function testMonthlyTrends() {
            const data = { ...testData, months: 6 };
            const result = await makeRequest('monthly-trends', data);
            displayResult('trendsResult', result, 'اختبار الاتجاهات الشهرية');
        }

        async function testActiveTasks() {
            const result = await makeRequest('active-tasks');
            displayResult('tasksResult', result, 'اختبار المهام النشطة');
        }

        async function testAll() {
            const allElement = document.getElementById('allResult');
            allElement.className = 'result';
            allElement.textContent = '🔄 جاري تشغيل جميع الاختبارات...';

            const tests = [
                { name: 'الفئات العمرية', endpoint: 'age-groups' },
                { name: 'المخزون', endpoint: 'stock-levels' },
                { name: 'الاتجاهات الشهرية', endpoint: 'monthly-trends' },
                { name: 'المهام النشطة', endpoint: 'active-tasks' }
            ];

            const results = [];
            
            for (const test of tests) {
                const data = test.endpoint === 'monthly-trends' ? { ...testData, months: 6 } : testData;
                const result = await makeRequest(test.endpoint, data);
                results.push({
                    name: test.name,
                    success: result.success,
                    data: result.success ? result.data : result.error
                });
            }

            const successCount = results.filter(r => r.success).length;
            const timestamp = new Date().toLocaleString('ar-SA');
            
            let output = `📊 نتائج الاختبار الشامل (${timestamp})\n`;
            output += `✅ نجح: ${successCount}/${results.length}\n`;
            output += `❌ فشل: ${results.length - successCount}/${results.length}\n\n`;

            results.forEach(result => {
                output += `${result.success ? '✅' : '❌'} ${result.name}\n`;
                if (!result.success) {
                    output += `   خطأ: ${result.data}\n`;
                }
                output += '\n';
            });

            if (successCount === results.length) {
                allElement.className = 'result success';
            } else if (successCount > 0) {
                allElement.className = 'result';
            } else {
                allElement.className = 'result error';
            }

            allElement.textContent = output;
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🧪 صفحة اختبار APIs الرسوم البيانية جاهزة');
            console.log('💡 استخدم الأزرار لاختبار APIs المختلفة');
        });
    </script>
</body>
</html>
